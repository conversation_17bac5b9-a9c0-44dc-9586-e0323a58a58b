/*  import google fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Ubuntu:wght@400;500;700&display=swap');

:root {
    --kali-blue: #0066cc;
    --kali-dark-blue: #004080;
    --kali-black: #121212;
    --kali-gray: #333333;
    --kali-light-gray: #555555;
    --kali-terminal-green: #00ff00;
    --kali-terminal-red: #ff0000;
    --kali-terminal-yellow: #ffff00;
    --terminal-bg: rgba(0, 0, 0, 0.85);
    --terminal-text: #00ff00;

    /* Dark Web Theme Colors */
    --darkweb-purple: #6e0b75;
    --darkweb-purple-glow: rgba(110, 11, 117, 0.6);
    --darkweb-red: #8b0000;
    --darkweb-red-glow: rgba(139, 0, 0, 0.6);
    --darkweb-cyan: #00ffff;
    --darkweb-cyan-glow: rgba(0, 255, 255, 0.4);
    --darkweb-bg: #0a0a0a;
    --darkweb-text-shadow: 0 0 8px rgba(0, 255, 255, 0.7);
    --darkweb-box-shadow: 0 0 15px rgba(110, 11, 117, 0.7);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    text-decoration: none;
}

html {
    scroll-behavior: smooth;
}

body {
    background-color: var(--darkweb-bg);
    color: #fff;
    font-family: 'Fira Code', monospace;
    position: relative;
}

/* Dark web overlay effect */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        repeating-linear-gradient(0deg, rgba(0,0,0, 0.11) 0px, rgba(0,0,0, 0.11) 12px, rgba(1,1,1, 0.16) 12px, rgba(1,1,1, 0.16) 24px, rgba(0,0,0, 0.14) 24px, rgba(0,0,0, 0.14) 36px, rgba(0,0,0, 0.23) 36px, rgba(0,0,0, 0.23) 48px, rgba(0,0,0, 0.12) 48px, rgba(0,0,0, 0.12) 60px, rgba(1,1,1, 0.07) 60px, rgba(1,1,1, 0.07) 72px),
        repeating-linear-gradient(90deg, rgba(0,0,0, 0.11) 0px, rgba(0,0,0, 0.11) 12px, rgba(1,1,1, 0.16) 12px, rgba(1,1,1, 0.16) 24px, rgba(0,0,0, 0.14) 24px, rgba(0,0,0, 0.14) 36px, rgba(0,0,0, 0.23) 36px, rgba(0,0,0, 0.23) 48px, rgba(0,0,0, 0.12) 48px, rgba(0,0,0, 0.12) 60px, rgba(1,1,1, 0.07) 60px, rgba(1,1,1, 0.07) 72px);
    background-size: 72px 72px;
    opacity: 0.3;
    z-index: -1;
    pointer-events: none;
}

/* custom scroll bar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: var(--kali-black);
}

::-webkit-scrollbar-thumb {
    background: var(--darkweb-purple);
    border-radius: 5px;
    box-shadow: 0 0 5px var(--darkweb-purple-glow);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--darkweb-cyan);
    box-shadow: 0 0 8px var(--darkweb-cyan-glow);
}

/* Three.js canvas styling */
#bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

/* all similar content styling codes */
section {
    padding: 100px 0;
}

.max-width {
    max-width: 1300px;
    padding: 0 80px;
    margin: auto;
}

.about,
.services,
.skills,
.teams,
.contact,
footer {
    font-family: 'Poppins', sans-serif;
}

.about .about-content,
.services .serv-content,
.skills .skills-content,
.contact .contact-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}
.social-icon {
    color: grey;
}
section .title {
    position: relative;
    text-align: center;
    font-size: 36px;
    font-weight: 500;
    margin-bottom: 60px;
    padding-bottom: 20px;
    font-family: 'Fira Code', monospace;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    letter-spacing: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

section .title i {
    color: var(--darkweb-purple);
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
    animation: pulse-icon 2s infinite alternate;
}

@keyframes pulse-icon {
    0% {
        opacity: 0.7;
        transform: scale(1);
    }
    100% {
        opacity: 1;
        transform: scale(1.1);
    }
}

.tor-logo-title {
    width: 30px;
    height: 30px;
    filter: drop-shadow(0 0 5px var(--darkweb-cyan-glow));
    animation: rotate 8s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

section .title::before {
    content: "";
    position: absolute;
    bottom: 0px;
    left: 50%;
    width: 180px;
    height: 3px;
    background: var(--darkweb-purple);
    transform: translateX(-50%);
    box-shadow: 0 0 10px var(--darkweb-purple-glow);
}

section .title::after {
    position: absolute;
    bottom: -8px;
    left: 50%;
    font-size: 20px;
    color: var(--darkweb-purple);
    padding: 0 5px;
    background: var(--darkweb-bg);
    transform: translateX(-50%);
    font-family: 'Fira Code', monospace;
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
}

/* navbar styling */
.navbar {
    position: fixed;
    width: 100%;
    z-index: 999;
    padding: 20px 0;
    font-family: 'Fira Code', monospace;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--darkweb-purple);
    box-shadow: 0 0 20px var(--darkweb-purple-glow);
}

.navbar.sticky {
    padding: 15px 0;
    background: rgba(0, 0, 0, 0.9);
}

.navbar .max-width {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.navbar .logo a {
    color: var(--darkweb-cyan);
    font-size: 28px;
    font-weight: 600;
    font-family: 'Fira Code', monospace;
    text-shadow: var(--darkweb-text-shadow);
}

.navbar .logo a span {
    color: var(--darkweb-purple);
    transition: all 0.3s ease;
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
}

.navbar.sticky .logo a span {
    color: var(--kali-terminal-green);
}

.navbar .menu li {
    list-style: none;
    display: inline-block;
}

.navbar .menu li a {
    display: block;
    color: #fff;
    font-size: 18px;
    font-weight: 500;
    margin-left: 25px;
    transition: color 0.3s ease;
}

.navbar .menu li a:hover {
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.navbar.sticky .menu li a:hover {
    color: var(--darkweb-purple);
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
}

/* menu btn styling */
.menu-btn {
    color: #fff;
    font-size: 23px;
    cursor: pointer;
    display: none;
}

.scroll-up-btn {
    position: fixed;
    height: 45px;
    width: 42px;
    background: var(--darkweb-purple);
    right: 30px;
    bottom: 30px;
    text-align: center;
    line-height: 45px;
    color: #fff;
    z-index: 9999;
    font-size: 30px;
    border-radius: 6px;
    border: 1px solid var(--darkweb-purple);
    cursor: pointer;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px var(--darkweb-purple-glow);
}

.scroll-up-btn.show {
    bottom: 30px;
    opacity: 1;
    pointer-events: auto;
}

.scroll-up-btn:hover {
    background: transparent;
    color: var(--darkweb-cyan);
    border-color: var(--darkweb-cyan);
    box-shadow: 0 0 15px var(--darkweb-cyan-glow);
}



/* home section styling */
.home {
    display: flex;
    height: 100vh;
    color: var(--terminal-text);
    min-height: 500px;
    font-family: 'Fira Code', monospace;
}

/* Terminal styling */
.terminal-window {
    background-color: var(--terminal-bg);
    border-radius: 8px;
    box-shadow: 0 0 25px var(--darkweb-purple-glow);
    overflow: hidden;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid var(--darkweb-purple);
    position: relative;
}

.terminal-window::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--darkweb-purple-glow) 0%, transparent 70%);
    opacity: 0.2;
    pointer-events: none;
}

.terminal-header {
    background-color: #1a1a1a;
    padding: 10px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--darkweb-purple);
    background-image: linear-gradient(to right, rgba(110, 11, 117, 0.1), transparent);
}

.terminal-buttons {
    display: flex;
    gap: 8px;
    margin-right: 15px;
}

.terminal-button {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.terminal-button.close {
    background-color: var(--darkweb-red);
    box-shadow: 0 0 5px var(--darkweb-red-glow);
}

.terminal-button.minimize {
    background-color: var(--darkweb-purple);
    box-shadow: 0 0 5px var(--darkweb-purple-glow);
}

.terminal-button.maximize {
    background-color: var(--darkweb-cyan);
    box-shadow: 0 0 5px var(--darkweb-cyan-glow);
}

.terminal-title {
    color: #fff;
    font-size: 14px;
    flex-grow: 1;
    text-align: center;
}

.terminal-body {
    padding: 20px;
    font-family: 'Fira Code', monospace;
}

.terminal-line {
    color: var(--darkweb-purple);
    margin-bottom: 10px;
    font-weight: 500;
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
}

.terminal-line::before {
    content: "$ ";
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.terminal-btn {
    display: inline-block;
    background: var(--darkweb-purple);
    color: #fff;
    font-size: 18px;
    padding: 8px 16px;
    margin-top: 15px;
    border-radius: 4px;
    border: 1px solid var(--darkweb-purple);
    transition: all 0.3s ease;
    font-family: 'Fira Code', monospace;
    box-shadow: 0 0 10px var(--darkweb-purple-glow);
}

.terminal-btn:hover {
    background: transparent;
    color: var(--darkweb-cyan);
    border-color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    box-shadow: 0 0 15px var(--darkweb-cyan-glow);
}

.home .max-width {
    width: 100%;
    display: flex;
}

.home .max-width .row {
    margin-right: 0;
}

.home .home-content .text-1 {
    font-size: 27px;
}

.home .home-content .text-2 {
    font-size: 75px;
    font-weight: 600;
    margin-left: -3px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.tor-logo {
    width: 40px;
    height: 40px;
    animation: pulse 2s infinite;
    filter: drop-shadow(0 0 5px var(--darkweb-cyan-glow));
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.home .home-content .text-3 {
    font-size: 40px;
    margin: 5px 0;
}

.home .home-content .text-3 span {
    color: var(--kali-blue);
    font-weight: 500;
    text-shadow: 0 0 5px rgba(0, 102, 204, 0.7);
}

/* about section styling */
.about .title::after {
    content: "who i am";
}

.about .about-content .left {
    width: 45%;
}

.about .about-content .left img {
    height: 400px;
    width: 400px;
    object-fit: cover;
    border-radius: 6px;
}

.about .about-content .right {
    width: 55%;
}

.about .about-content .right .text {
    font-size: 25px;
    font-weight: 600;
    margin-bottom: 10px;
}

.about .about-content .right .text span {
    color: var(--kali-blue);
    text-shadow: 0 0 5px rgba(0, 102, 204, 0.5);
}

.about .about-content .right p {
    text-align: justify;
}

.about .about-content .right a {
    display: inline-block;
    background: var(--kali-blue);
    color: #fff;
    font-size: 20px;
    font-weight: 500;
    padding: 10px 30px;
    margin-top: 20px;
    border-radius: 6px;
    border: 2px solid var(--kali-blue);
    transition: all 0.3s ease;
    font-family: 'Fira Code', monospace;
}

.about .about-content .right a:hover {
    color: var(--kali-terminal-green);
    background: transparent;
    border-color: var(--kali-terminal-green);
    text-shadow: 0 0 5px var(--kali-terminal-green);
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}


/* services section styling */
.services,
.teams {
    color: #fff;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    border-top: 1px solid var(--darkweb-purple);
    border-bottom: 1px solid var(--darkweb-purple);
    box-shadow: 0 0 20px var(--darkweb-purple-glow);
    position: relative;
    overflow: hidden;
}

.services::before,
.teams::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--darkweb-purple-glow) 0%, transparent 100%);
    opacity: 0.1;
    pointer-events: none;
}

.services .title::before,
.teams .title::before {
    background: var(--kali-blue);
}

.services .title::after,
.teams .title::after {
    background: var(--kali-black);
    color: var(--kali-blue);
    content: "what i provide";
}

.services .serv-content .card {
    width: calc(33% - 20px);
    background: rgba(0, 0, 0, 0.8);
    text-align: center;
    border-radius: 6px;
    padding: 50px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--darkweb-purple);
    box-shadow: 0 0 15px var(--darkweb-purple-glow);
    position: relative;
    overflow: hidden;
}

.services .serv-content .card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--darkweb-purple-glow) 0%, transparent 70%);
    opacity: 0.1;
    pointer-events: none;
}

.services .serv-content .card:hover {
    background: rgba(110, 11, 117, 0.15);
    border-color: var(--darkweb-cyan);
    box-shadow: 0 0 20px var(--darkweb-cyan-glow);
}

.services .serv-content .card .box {
    transition: all 0.3s ease;
}

.services .serv-content .card:hover .box {
    transform: scale(1.05);
}

.services .serv-content .card i {
    font-size: 50px;
    color: var(--darkweb-purple);
    transition: all 0.3s ease;
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
}

.services .serv-content .card:hover i {
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    transform: scale(1.1);
}

.services .serv-content .card .text {
    font-size: 25px;
    font-weight: 500;
    margin: 10px 0 7px 0;
}


/*ressources section styling */
.ressources {
    color: #fff;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    border-top: 1px solid var(--kali-blue);
    border-bottom: 1px solid var(--kali-blue);
    font-family: 'Fira Code', monospace;
}

.ressources a {
    color: var(--kali-blue);
    text-decoration: none;
    transition: all 0.3s ease;
}

.ressources a:hover {
    color: var(--kali-terminal-green);
    text-shadow: 0 0 5px var(--kali-terminal-green);
}

.ressources ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.ressources .title::before {
    background: var(--kali-blue);
}

.ressources .title::after {
    content: "what i use";
    background: var(--kali-black);
    color: var(--kali-blue);
}

.ressources .serv-content .card {
    width: calc(33% - 20px);
    background: rgba(0, 0, 0, 0.8);
    text-align: center;
    border-radius: 6px;
    padding: 50px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid var(--kali-gray);
    box-shadow: 0 0 15px rgba(0, 102, 204, 0.3);
}

.ressources .serv-content .card:hover {
    background: rgba(0, 102, 204, 0.2);
    border-color: var(--kali-blue);
    box-shadow: 0 0 20px rgba(0, 102, 204, 0.5);
}

.ressources .serv-content .card .box {
    transition: all 0.3s ease;
}

.ressources .serv-content .card:hover .box {
    transform: scale(1.05);
}

.ressources .serv-content .card i {
    font-size: 50px;
    color: var(--kali-blue);
    transition: color 0.3s ease;
}

.ressources .serv-content .card:hover i {
    color: var(--kali-terminal-green);
    text-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}

.ressources .serv-content .card .text {
    font-size: 25px;
    font-weight: 500;
    margin: 10px 0 7px 0;
    color: #fff;
}

.ressources .serv-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
}

.ressources .serv-content>div:first-child {
    margin-bottom: 20px;
}

.ressources .serv-content .card {
    width: calc(50% - 10px);
    margin-bottom: 20px;
    background: #cacaca;
    text-align: center;
    border-radius: 6px;
    padding: 50px 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}


  .ressources .serv-content .card:hover {
    background-image: none;

  }
/*Add the media query for the ressources section */
@media (min-width: 768px) {
    .ressources .serv-content .card {
        width: calc(50% - 20px);
    }
}
@media (max-width: 947px) {
    .ressources .serv-content .card {
        width: calc(50% - 20px);
    }
}

@media (max-width: 690px) {
    .ressources .serv-content .card {
        width: 100%;
    }
}

@media (max-width: 500px) {
    .ressources .serv-content .card {
        width: 100%;
    }
}




/* skills section styling */

.skills .title::after {
    content: "what i know";
}

.skills .skills-content .column {
    width: calc(50% - 30px);
}

.skills .skills-content .left .text {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.skills .skills-content .left p {
    text-align: justify;
    line-height: 1.6;
}

.skills-terminal {
    width: 100%;
    height: 100%;
    min-height: 300px;
}

.skills-btn {
    margin-top: 15px;
    position: relative;
    overflow: hidden;
}

.skills-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.skills-btn:hover::before {
    left: 100%;
}

.skills .skills-content .right .bars {
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px 15px;
    border: 1px solid var(--darkweb-purple);
    box-shadow: 0 0 15px var(--darkweb-purple-glow);
    transition: all 0.3s ease;
}

.skills .skills-content .right .bars:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 20px var(--darkweb-cyan-glow);
    border-color: var(--darkweb-cyan);
}

.skills .skills-content .right .info {
    display: flex;
    margin-bottom: 8px;
    align-items: center;
    justify-content: space-between;
}

.skills .skills-content .right .info i {
    margin-right: 10px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    transition: all 0.3s ease;
}

.skills .skills-content .right .bars:hover .info i {
    color: var(--darkweb-purple);
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
    transform: scale(1.2);
}

.skills .skills-content .right span {
    font-weight: 500;
    font-size: 16px;
    font-family: 'Fira Code', monospace;
}

.skills .skills-content .right .percentage-value {
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    font-weight: bold;
}

.skills .skills-content .right .line {
    height: 8px;
    width: 100%;
    background: rgba(0, 0, 0, 0.5);
    position: relative;
    border-radius: 10px;
    overflow: hidden;
}

.skills .skills-content .right .line::before {
    content: "";
    position: absolute;
    height: 100%;
    left: 0;
    top: 0;
    background: var(--darkweb-purple);
    box-shadow: 0 0 10px var(--darkweb-purple-glow);
    border-radius: 10px;
}

.skills .skills-content .right .line-progress {
    position: absolute;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.1), transparent);
    animation: progress-animation 2s linear infinite;
}

@keyframes progress-animation {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Skill bar animations are now generated dynamically via JavaScript */
.skills-content .right .line::before {
    width: 0; /* Starting width - will be set by JavaScript */
}

/* teams section styling */
.teams .title::after {
    content: "dark web projects";
}

/* Dark web banner */
.dark-web-banner {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--darkweb-purple);
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 0 20px var(--darkweb-purple-glow);
    position: relative;
    overflow: hidden;
}

.dark-web-banner::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--darkweb-purple-glow) 0%, transparent 70%);
    opacity: 0.2;
    pointer-events: none;
}

.dark-web-banner .glitch-text {
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 3px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    font-family: 'Fira Code', monospace;
    margin-bottom: 10px;
    cursor: pointer;
}

.dark-web-banner.active-glitch .glitch-text {
    animation: intense-glitch 0.5s linear;
}

@keyframes intense-glitch {
    0% {
        text-shadow: 0.05em 0 0 var(--darkweb-purple), -0.05em -0.025em 0 var(--darkweb-cyan);
        opacity: 1;
    }
    14% {
        text-shadow: 0.05em 0 0 var(--darkweb-purple), -0.05em -0.025em 0 var(--darkweb-cyan);
        opacity: 1;
    }
    15% {
        text-shadow: -0.05em -0.025em 0 var(--darkweb-purple), 0.025em 0.025em 0 var(--darkweb-cyan);
        opacity: 0.8;
    }
    49% {
        text-shadow: -0.05em -0.025em 0 var(--darkweb-purple), 0.025em 0.025em 0 var(--darkweb-cyan);
        opacity: 0.8;
    }
    50% {
        text-shadow: 0.025em 0.05em 0 var(--darkweb-purple), 0.05em 0 0 var(--darkweb-cyan);
        opacity: 1;
    }
    99% {
        text-shadow: 0.025em 0.05em 0 var(--darkweb-purple), 0.05em 0 0 var(--darkweb-cyan);
        opacity: 1;
    }
    100% {
        text-shadow: -0.025em 0 0 var(--darkweb-purple), -0.025em -0.025em 0 var(--darkweb-cyan);
        opacity: 0.9;
    }
}

.encrypted-text {
    font-family: 'Fira Code', monospace;
    color: var(--darkweb-purple);
    font-size: 14px;
    letter-spacing: 1px;
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
    position: relative;
}

.encrypted-text::before {
    content: "";
    position: absolute;
    left: 50%;
    top: -5px;
    width: 50px;
    height: 1px;
    background: var(--darkweb-purple);
    transform: translateX(-50%);
    box-shadow: 0 0 5px var(--darkweb-purple-glow);
}

.teams .carousel .card {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 6px;
    padding: 25px 35px;
    text-align: center;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--darkweb-purple);
    box-shadow: 0 0 15px var(--darkweb-purple-glow);
    position: relative;
}

.teams .carousel .card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--darkweb-purple-glow) 0%, transparent 70%);
    opacity: 0.1;
    pointer-events: none;
}

.teams .carousel .card:hover {
    background: rgba(110, 11, 117, 0.15);
    border-color: var(--darkweb-cyan);
    box-shadow: 0 0 20px var(--darkweb-cyan-glow);
    transform: translateY(-5px);
}

.teams .carousel .card .box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.teams .carousel .card:hover .box {
    transform: scale(1.05);
}

.teams .carousel .card .text {
    font-size: 22px;
    font-weight: 500;
    margin: 10px 0 7px 0;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    font-family: 'Fira Code', monospace;
}

.project-link {
    display: inline-block;
    font-size: 22px;
    font-weight: 500;
    margin: 10px 0 7px 0;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    font-family: 'Fira Code', monospace;
    transition: all 0.3s ease;
}

.project-link:hover {
    color: var(--darkweb-purple);
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
}

.project-link i {
    margin-right: 5px;
}

/* Project tech tags */
.project-tech {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin-top: 15px;
}

.tech-tag {
    background: var(--darkweb-purple);
    color: #fff;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-family: 'Fira Code', monospace;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px var(--darkweb-purple-glow);
    position: relative;
    overflow: hidden;
}

.tech-tag::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.tech-tag:hover::before {
    left: 100%;
}

.tag-highlight {
    background: var(--darkweb-cyan) !important;
    box-shadow: 0 0 8px var(--darkweb-cyan-glow) !important;
    transform: translateY(-2px);
}

.teams .carousel .card:hover .tech-tag {
    background: var(--darkweb-cyan);
    box-shadow: 0 0 8px var(--darkweb-cyan-glow);
}

/* Project image container and overlay */
.project-image-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto 15px;
    overflow: hidden;
    border-radius: 50%;
    border: 5px solid var(--darkweb-purple);
    transition: all 0.3s ease;
    box-shadow: 0 0 15px var(--darkweb-purple-glow);
}

.project-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-overlay i {
    font-size: 40px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.teams .carousel .card:hover .project-image-container {
    border-color: var(--darkweb-cyan);
    box-shadow: 0 0 20px var(--darkweb-cyan-glow);
}

.teams .carousel .card:hover .project-overlay {
    opacity: 1;
}

.teams .carousel .card:hover img {
    transform: scale(1.1);
}

.owl-dots {
    text-align: center;
    margin-top: 20px;
}

.owl-dot {
    height: 13px;
    width: 13px;
    margin: 0 5px;
    outline: none !important;
    border-radius: 50%;
    border: 2px solid var(--darkweb-purple) !important;
    transition: all 0.3s ease;
    box-shadow: 0 0 5px var(--darkweb-purple-glow);
}

.owl-dot.active {
    width: 35px;
    border-radius: 14px;
}

.owl-dot.active,
.owl-dot:hover {
    background: var(--darkweb-purple) !important;
    box-shadow: 0 0 10px var(--darkweb-purple-glow);
}

.owl-dot.active:hover {
    background: var(--darkweb-cyan) !important;
    border-color: var(--darkweb-cyan) !important;
    box-shadow: 0 0 10px var(--darkweb-cyan-glow);
}

/* contact section styling */
.contact .title::after {
    content: "secure channel";
}

.contact .contact-content .column {
    width: calc(50% - 30px);
}

/* Contact banner */
.contact-banner {
    margin-bottom: 30px;
}

/* Contact terminals */
.contact-terminal, .message-terminal {
    height: 100%;
    min-height: 400px;
}

/* Contact info styling */
.contact .contact-content .icons {
    margin: 20px 0;
}

.contact .contact-content .row {
    display: flex;
    height: 65px;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
    transition: all 0.3s ease;
}

.contact .contact-content .row:hover {
    transform: translateX(10px);
}

.contact .contact-content .row .info {
    margin-left: 20px;
}

.contact .contact-content .row i {
    font-size: 25px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    width: 40px;
    height: 40px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--darkweb-purple);
    box-shadow: 0 0 10px var(--darkweb-purple-glow);
    transition: all 0.3s ease;
}

.contact .contact-content .row:hover i {
    color: var(--darkweb-purple);
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
    border-color: var(--darkweb-cyan);
    box-shadow: 0 0 15px var(--darkweb-cyan-glow);
    transform: rotate(15deg);
}

.contact .contact-content .info .head {
    font-weight: 500;
    color: var(--darkweb-purple);
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    letter-spacing: 1px;
}

.contact .contact-content .info .sub-title {
    color: #fff;
    font-family: 'Fira Code', monospace;
}

.pgp-key {
    font-family: 'Fira Code', monospace;
    letter-spacing: 1px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.verification-status {
    margin-top: 15px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    font-family: 'Fira Code', monospace;
    display: inline-block;
    padding: 5px 15px;
    border: 1px solid var(--darkweb-cyan);
    border-radius: 4px;
    background: rgba(0, 255, 255, 0.05);
}

.verification-status i {
    margin-left: 8px;
    color: #00ff00;
}

/* Encrypted form styling */
.encrypted-form .fields {
    display: flex;
    gap: 15px;
}

.encrypted-form .field,
.encrypted-form .fields .field {
    height: 45px;
    width: 100%;
    margin-bottom: 15px;
    position: relative;
}

.encrypted-form .textarea {
    height: 120px;
    width: 100%;
    position: relative;
}

.encrypted-form .field input,
.encrypted-form .textarea textarea {
    height: 100%;
    width: 100%;
    border: 1px solid var(--darkweb-purple);
    border-radius: 6px;
    outline: none;
    padding: 0 15px 0 40px;
    font-size: 16px;
    font-family: 'Fira Code', monospace;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.3);
    color: #fff;
    box-shadow: 0 0 10px rgba(110, 11, 117, 0.3);
}

.encrypted-form .field input:focus,
.encrypted-form .textarea textarea:focus {
    border-color: var(--darkweb-cyan);
    box-shadow: 0 0 15px var(--darkweb-cyan-glow);
}

.encrypted-form .textarea textarea {
    padding: 10px 15px 10px 40px;
    resize: none;
}

.field-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--darkweb-purple);
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
    transition: all 0.3s ease;
}

.textarea-icon {
    top: 20px;
    transform: none;
}

.encrypted-form .field:focus-within .field-icon {
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.encryption-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid var(--darkweb-purple);
    border-radius: 6px;
    font-family: 'Fira Code', monospace;
    font-size: 14px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.encryption-icon i {
    color: var(--darkweb-purple);
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
    animation: pulse 2s infinite;
}

.encrypted-form .button-area {
    display: flex;
    align-items: center;
}

.encrypted-form .button-area button {
    color: #fff;
    display: block;
    width: 100% !important;
    height: 45px;
    outline: none;
    font-size: 16px;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    flex-wrap: nowrap;
    background: var(--darkweb-purple);
    border: 2px solid var(--darkweb-purple);
    transition: all 0.3s ease;
    font-family: 'Fira Code', monospace;
    box-shadow: 0 0 15px var(--darkweb-purple-glow);
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.encrypted-form .button-area button::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: 0.5s;
}

.encrypted-form .button-area button:hover::before {
    left: 100%;
}

.encrypted-form .button-area button:hover {
    color: var(--darkweb-cyan);
    background: transparent;
    border-color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    box-shadow: 0 0 15px var(--darkweb-cyan-glow);
}

/* Terminal success message */
.terminal-success {
    margin-top: 20px;
    padding: 10px 15px;
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid var(--darkweb-cyan);
    border-radius: 6px;
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    font-family: 'Fira Code', monospace;
    text-align: center;
    animation: pulse 2s infinite;
    box-shadow: 0 0 15px var(--darkweb-cyan-glow);
}

/* footer section styling */
footer {
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-align: center;
    border-top: 1px solid var(--darkweb-purple);
    font-family: 'Fira Code', monospace;
    box-shadow: 0 0 20px var(--darkweb-purple-glow);
    position: relative;
    overflow: hidden;
}

.footer-content {
    padding: 15px 23px;
    position: relative;
    z-index: 2;
    background: rgba(0, 0, 0, 0.7);
}

.footer-content::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--darkweb-purple-glow) 0%, transparent 70%);
    opacity: 0.2;
    pointer-events: none;
    z-index: -1;
}

/* Footer landscape styling */
.footer-landscape {
    position: relative;
    width: 100%;
    height: 300px; /* Increased height to show more of the image */
    overflow: hidden;
}

.footer-landscape-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center 30%; /* Adjusted to show more of the eyes area */
    filter: grayscale(30%) brightness(0.85) contrast(1.1);
    transition: all 0.5s ease;
}

.footer-landscape:hover .footer-landscape-img {
    filter: grayscale(30%) brightness(0.8) contrast(1.3);
    transform: scale(1.05);
}

.footer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 0%, rgba(0, 0, 0, 0.5) 100%);
    z-index: 1;
}

.footer-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    text-align: center;
    width: 100%;
    padding: 20px;
}

.footer-message .glitch-text {
    font-size: 38px;
    font-weight: bold;
    letter-spacing: 3px;
    color: var(--darkweb-cyan);
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.8), 0 0 30px var(--darkweb-cyan-glow);
    margin-bottom: 15px;
}

.footer-message .encrypted-text {
    margin-bottom: 10px;
    color: #fff;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.8), 0 0 20px rgba(0, 0, 0, 0.8);
    font-size: 18px;
    font-weight: 500;
}

.footer-message .terminal-line {
    display: inline-block;
    color: var(--darkweb-purple);
    text-shadow: 0 0 5px var(--darkweb-purple-glow), 0 0 10px rgba(0, 0, 0, 0.8);
    font-family: 'Fira Code', monospace;
    font-size: 16px;
    padding: 5px 15px;
    border: 1px solid var(--darkweb-purple);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.7);
    box-shadow: 0 0 10px var(--darkweb-purple-glow), 0 0 20px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.footer-message .terminal-line:hover {
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    border-color: var(--darkweb-cyan);
    box-shadow: 0 0 15px var(--darkweb-cyan-glow);
    cursor: pointer;
}

footer span a {
    color: var(--darkweb-purple);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

footer span a:hover {
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
}

.onion-address {
    margin-top: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-family: 'Fira Code', monospace;
    color: var(--darkweb-purple);
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
    position: relative;
    z-index: 1;
}

.tor-logo-small {
    width: 20px;
    height: 20px;
    filter: drop-shadow(0 0 3px var(--darkweb-cyan-glow));
}

.onion-address .glitch-text {
    font-size: 14px;
    letter-spacing: 1px;
}

.onion-address:hover .glitch-text {
    color: var(--darkweb-cyan);
    text-shadow: var(--darkweb-text-shadow);
    transition: all 0.3s ease;
}


/* Terminal Modal for Skills Section */
.terminal-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
    animation: modal-fade-in 0.3s ease-in-out;
}

@keyframes modal-fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
}

.terminal-modal-content {
    width: 80%;
    max-width: 600px;
    background-color: var(--terminal-bg);
    border: 1px solid var(--darkweb-purple);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0 30px var(--darkweb-purple-glow);
    position: relative;
    font-family: 'Fira Code', monospace;
    max-height: 80vh;
    overflow-y: auto;
}

.terminal-modal-content::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, var(--darkweb-purple-glow) 0%, transparent 70%);
    opacity: 0.2;
    pointer-events: none;
    border-radius: 8px;
}

.terminal-modal-close {
    position: absolute;
    top: 10px;
    right: 15px;
    color: var(--darkweb-cyan);
    font-size: 24px;
    cursor: pointer;
    text-shadow: var(--darkweb-text-shadow);
    z-index: 1;
}

.terminal-modal-close:hover {
    color: var(--darkweb-purple);
    text-shadow: 0 0 10px var(--darkweb-purple-glow);
}

.terminal-line-output {
    color: var(--darkweb-cyan);
    margin-bottom: 8px;
    text-shadow: var(--darkweb-text-shadow);
    position: relative;
    z-index: 1;
    font-size: 14px;
    line-height: 1.6;
}

.terminal-line-output:nth-child(odd) {
    color: var(--darkweb-purple);
    text-shadow: 0 0 5px var(--darkweb-purple-glow);
}

/* responsive media query start */

/* High-resolution displays (Retina) */
@media only screen and (-webkit-min-device-pixel-ratio: 2),
       only screen and (min-resolution: 192dpi) {
    /* Enhance shadows and glows for high-res displays */
    .darkweb-text-shadow {
        text-shadow: 0 0 8px rgba(0, 255, 255, 0.8), 0 0 12px rgba(0, 255, 255, 0.5);
    }

    .darkweb-purple-glow {
        box-shadow: 0 0 15px rgba(110, 11, 117, 0.8), 0 0 25px rgba(110, 11, 117, 0.5);
    }

    .darkweb-cyan-glow {
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.8), 0 0 25px rgba(0, 255, 255, 0.5);
    }

    /* Enhance borders for high-res displays */
    .terminal-window {
        border-width: 1.5px;
    }

    /* Enhance text rendering */
    body {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
}

/* Large desktop and laptops */
@media (max-width: 1440px) {
    .about .about-content .left img {
        height: 380px;
        width: 380px;
    }

    .max-width {
        padding: 0 80px;
    }

    /* Adjust Three.js canvas for larger screens */
    #bg-canvas {
        opacity: 0.7;
    }
}

/* Standard desktop and smaller laptops */
@media (max-width: 1200px) {
    .about .about-content .left img {
        height: 350px;
        width: 350px;
    }

    .max-width {
        padding: 0 60px;
    }

    /* Adjust terminal windows */
    .terminal-window {
        min-height: 280px;
    }

    /* Adjust footer landscape */
    .footer-landscape {
        height: 280px;
    }
}

/* Small desktop and large tablets */
@media (max-width: 991px) {
    .max-width {
        padding: 0 50px;
    }

    /* Adjust dark web banners */
    .dark-web-banner .glitch-text {
        font-size: 22px;
    }

    /* Adjust terminal windows */
    .terminal-window {
        min-height: 260px;
    }

    /* Adjust footer landscape */
    .footer-message .glitch-text {
        font-size: 32px;
    }
}

@media (max-width: 947px) {
    .menu-btn {
        display: block;
        z-index: 999;
    }

    .menu-btn i.active:before {
        content: "\f00d";
    }

    .navbar .menu {
        position: fixed;
        height: 100vh;
        width: 100%;
        left: -100%;
        top: 0;
        background: #111;
        text-align: center;
        padding-top: 80px;
        transition: all 0.3s ease;
    }

    .navbar .menu.active {
        left: 0;
    }

    .navbar .menu li {
        display: block;
    }

    .navbar .menu li a {
        display: inline-block;
        margin: 20px 0;
        font-size: 25px;
    }

    .home .home-content .text-2 {
        font-size: 70px;
    }

    .home .home-content .text-3 {
        font-size: 35px;
    }

    .home .home-content a {
        font-size: 23px;
        padding: 10px 30px;
    }

    .max-width {
        max-width: 930px;
    }

    .about .about-content .column {
        width: 100%;
    }

    .about .about-content .left {
        display: flex;
        justify-content: center;
        margin: 0 auto 60px;
    }

    .about .about-content .right {
        flex: 100%;
    }

    .services .serv-content .card {
        width: calc(50% - 10px);
        margin-bottom: 20px;
    }

    .skills .skills-content .column,
    .contact .contact-content .column {
        width: 100%;
        margin-bottom: 35px;
    }
}

/* Tablets and small laptops */
@media (max-width: 768px) {
    .max-width {
        padding: 0 30px;
    }

    .home .home-content .text-2 {
        font-size: 65px;
    }

    .home .home-content .text-3 {
        font-size: 35px;
    }

    /* Adjust terminal windows for tablets */
    .contact-terminal, .message-terminal, .skills-terminal {
        min-height: 300px;
    }

    /* Adjust footer landscape */
    .footer-landscape {
        height: 250px;
    }

    .footer-message .glitch-text {
        font-size: 30px;
    }

    /* Adjust project cards */
    .teams .carousel .card {
        padding: 20px 25px;
    }

    .project-image-container {
        width: 130px;
        height: 130px;
    }

    /* Adjust encryption form */
    .encrypted-form .fields {
        gap: 10px;
    }
}

/* Large phones and small tablets */
@media (max-width: 690px) {
    .max-width {
        padding: 0 23px;
    }

    .home .home-content .text-2 {
        font-size: 60px;
    }

    .home .home-content .text-3 {
        font-size: 32px;
    }

    .home .home-content a {
        font-size: 20px;
    }

    .services .serv-content .card {
        width: 100%;
    }

    /* Adjust terminal windows */
    .terminal-window {
        min-height: 240px;
    }

    .terminal-title {
        font-size: 14px;
    }

    /* Adjust footer landscape */
    .footer-landscape {
        height: 220px;
    }

    .footer-message .glitch-text {
        font-size: 26px;
    }

    /* Adjust contact section */
    .contact .contact-content .row i {
        font-size: 22px;
        width: 35px;
        height: 35px;
    }

    /* Adjust project cards */
    .tech-tag {
        font-size: 10px;
        padding: 2px 6px;
    }
}

/* Medium phones */
@media (max-width: 576px) {
    .home .home-content .text-2 {
        font-size: 55px;
    }

    .home .home-content .text-3 {
        font-size: 28px;
    }

    /* Adjust section titles */
    section .title {
        font-size: 32px;
    }

    /* Adjust terminal windows */
    .terminal-window {
        min-height: 220px;
    }

    /* Adjust footer landscape */
    .footer-landscape {
        height: 200px;
    }

    .footer-message .glitch-text {
        font-size: 24px;
    }

    /* Adjust contact section */
    .encrypted-form .fields {
        flex-direction: column;
        gap: 0;
    }

    .encrypted-form .button-area button {
        font-size: 14px;
    }

    /* Adjust project cards */
    .project-image-container {
        width: 110px;
        height: 110px;
    }
}

/* Small phones */
@media (max-width: 480px) {
    .home .home-content .text-2 {
        font-size: 50px;
    }

    .home .home-content .text-3 {
        font-size: 27px;
    }

    .about .about-content .right .text,
    .skills .skills-content .left .text {
        font-size: 19px;
    }

    .contact .right form .fields {
        flex-direction: column;
    }

    .contact .right form .name,
    .contact .right form .email {
        margin: 0;
    }

    .right form .error-box {
        width: 150px;
    }

    .scroll-up-btn {
        right: 15px;
        bottom: 15px;
        height: 38px;
        width: 35px;
        font-size: 23px;
        line-height: 38px;
    }

    /* Adjust terminal windows */
    .terminal-window {
        min-height: 200px;
    }

    .terminal-title {
        font-size: 12px;
    }

    /* Adjust footer landscape */
    .footer-landscape {
        height: 180px;
    }

    .footer-message .glitch-text {
        font-size: 22px;
    }

    .footer-message .encrypted-text {
        font-size: 14px;
    }

    .footer-message .terminal-line {
        font-size: 14px;
        padding: 4px 10px;
    }
}

/* Fix for iPhone SE and other small devices */
@media (max-width: 400px) {
    /* Fix for the encrypted form fields */
    .encrypted-form .field input,
    .encrypted-form .textarea textarea {
        font-size: 14px;
        padding-left: 35px;
    }

    .field-icon {
        left: 10px;
        font-size: 14px;
    }

    /* Fix for the terminal windows */
    .terminal-line {
        font-size: 12px;
    }

    /* Fix for the verification status */
    .verification-status {
        font-size: 12px;
        padding: 4px 10px;
    }

    /* Fix for the footer content */
    .footer-content {
        padding: 10px 15px;
        font-size: 14px;
    }

    .social-icon {
        font-size: 18px;
        margin: 0 5px;
    }

    .onion-address .glitch-text {
        font-size: 12px;
    }

    .tor-logo-small {
        width: 15px;
        height: 15px;
    }
}

/* Extra small phones */
@media (max-width: 380px) {
    .home .home-content .text-1 {
        font-size: 20px;
    }

    .home .home-content .text-2 {
        font-size: 45px;
    }

    .home .home-content .text-3 {
        font-size: 25px;
    }

    /* Adjust section titles */
    section .title {
        font-size: 28px;
        letter-spacing: 1px;
    }

    /* Adjust terminal windows */
    .terminal-window {
        min-height: 180px;
    }

    .terminal-body {
        padding: 10px;
    }

    /* Adjust footer landscape */
    .footer-landscape {
        height: 160px;
    }

    .footer-message .glitch-text {
        font-size: 20px;
    }

    /* Adjust contact section */
    .contact .contact-content .row i {
        font-size: 18px;
        width: 30px;
        height: 30px;
    }

    .contact .contact-content .info .head {
        font-size: 12px;
    }

    .contact .contact-content .info .sub-title {
        font-size: 14px;
    }

    /* Adjust project cards */
    .project-image-container {
        width: 90px;
        height: 90px;
    }

    .teams .carousel .card .text {
        font-size: 18px;
    }

    .project-tech {
        gap: 5px;
    }

    .tech-tag {
        font-size: 9px;
        padding: 2px 4px;
    }
}

/* Landscape mode fixes for mobile devices */
@media (max-height: 600px) and (orientation: landscape) {
    /* Adjust section padding for landscape */
    section {
        padding: 50px 0;
    }

    /* Adjust home section for landscape */
    .home {
        height: auto;
        min-height: 100vh;
    }

    /* Adjust terminal windows for landscape */
    .terminal-window {
        min-height: 200px;
    }

    /* Adjust footer landscape for landscape mode */
    .footer-landscape {
        height: 180px;
    }

    /* Adjust contact section for landscape */
    .contact-content {
        flex-direction: column;
    }

    .contact .contact-content .column {
        width: 100%;
    }

    /* Adjust menu for landscape */
    .navbar .menu {
        padding-top: 60px;
    }

    .navbar .menu li a {
        margin: 15px 0;
        font-size: 22px;
    }
}

/* iPhone 5/SE and extremely small devices */
@media (max-width: 320px) {
    .home .home-content .text-1 {
        font-size: 18px;
    }

    .home .home-content .text-2 {
        font-size: 40px;
    }

    .home .home-content .text-3 {
        font-size: 22px;
    }

    /* Adjust section titles */
    section .title {
        font-size: 24px;
        letter-spacing: 0;
    }

    /* Adjust terminal windows */
    .terminal-window {
        min-height: 160px;
    }

    /* Adjust footer landscape */
    .footer-landscape {
        height: 140px;
    }

    .footer-message .glitch-text {
        font-size: 18px;
    }

    /* Adjust contact section */
    .contact .contact-content .row i {
        font-size: 16px;
        width: 28px;
        height: 28px;
    }

    /* Adjust project cards */
    .project-image-container {
        width: 80px;
        height: 80px;
        border-width: 3px;
    }

    .teams .carousel .card {
        padding: 15px;
    }

    .teams .carousel .card .text {
        font-size: 16px;
    }

    /* Fix for the encrypted form fields */
    .encrypted-form .field input,
    .encrypted-form .textarea textarea {
        font-size: 12px;
        padding-left: 30px;
    }

    .field-icon {
        left: 8px;
        font-size: 12px;
    }

    .encryption-status {
        font-size: 11px;
    }

    .encrypted-form .button-area button {
        font-size: 12px;
        height: 40px;
    }
}