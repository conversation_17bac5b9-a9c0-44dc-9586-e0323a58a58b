// Three.js background implementation for Kali Linux theme

// Scene setup
let scene, camera, renderer, particles, smallParticles, networkLinks, dataPackets;
let mouseX = 0, mouseY = 0;
let networkNodes = [];
let connectionLines = [];
let activePackets = [];
const MAX_CONNECTIONS = 100; // Maximum number of connection lines
const MAX_PACKETS = 50; // Maximum number of data packets

// Initialize the scene
function init() {
    try {
        // Import Three.js from CDN
        const THREE = window.THREE;
        if (!THREE) {
            console.error('THREE.js not loaded');
            return;
        }

        // Create scene
        scene = new THREE.Scene();

        // Create camera
        camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 2000);
        camera.position.z = 1000;

        // Create renderer
        const canvas = document.getElementById('bg');
        if (!canvas) {
            console.error('Canvas element not found');
            return;
        }

        renderer = new THREE.WebGLRenderer({
            canvas: canvas,
            alpha: true
        });
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);

        // Create particle system (matrix-like effect)
        const particlesGeometry = new THREE.BufferGeometry();
        const particleCount = 5000;

        const posArray = new Float32Array(particleCount * 3);
        const colorArray = new Float32Array(particleCount * 3);

        // Fill with random positions
        for (let i = 0; i < particleCount * 3; i += 3) {
            // Position
            posArray[i] = (Math.random() - 0.5) * 2000;
            posArray[i + 1] = (Math.random() - 0.5) * 2000;
            posArray[i + 2] = (Math.random() - 0.5) * 2000;

            // Color - dark web theme (purple/cyan)
            // Randomly choose between purple and cyan particles
            if (Math.random() > 0.7) {
                // Purple particles
                colorArray[i] = 0.43;      // R (110/255)
                colorArray[i + 1] = 0.04;   // G (11/255)
                colorArray[i + 2] = 0.46;   // B (117/255)
            } else if (Math.random() > 0.4) {
                // Cyan particles
                colorArray[i] = 0;          // R
                colorArray[i + 1] = 0.8 + Math.random() * 0.2; // G
                colorArray[i + 2] = 0.8 + Math.random() * 0.2; // B
            } else {
                // Dark red particles
                colorArray[i] = 0.55;       // R (139/255)
                colorArray[i + 1] = 0;      // G
                colorArray[i + 2] = 0;      // B
            }
        }

        particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
        particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colorArray, 3));

        const particlesMaterial = new THREE.PointsMaterial({
            size: 2.5,
            vertexColors: true,
            transparent: true,
            opacity: 0.8,
            sizeAttenuation: true
        });

        // Add a second particle system for a more complex effect
        const smallParticlesGeometry = new THREE.BufferGeometry();
        const smallParticleCount = 2000;

        const smallPosArray = new Float32Array(smallParticleCount * 3);

        // Fill with random positions for small particles
        for (let i = 0; i < smallParticleCount * 3; i += 3) {
            smallPosArray[i] = (Math.random() - 0.5) * 1000;
            smallPosArray[i + 1] = (Math.random() - 0.5) * 1000;
            smallPosArray[i + 2] = (Math.random() - 0.5) * 1000;
        }

        smallParticlesGeometry.setAttribute('position', new THREE.BufferAttribute(smallPosArray, 3));

        const smallParticlesMaterial = new THREE.PointsMaterial({
            size: 1,
            color: 0x00ffff, // Cyan
            transparent: true,
            opacity: 0.6,
            sizeAttenuation: true
        });

        smallParticles = new THREE.Points(smallParticlesGeometry, smallParticlesMaterial);
        scene.add(smallParticles);

        particles = new THREE.Points(particlesGeometry, particlesMaterial);
        scene.add(particles);

        // Create network nodes (a subset of particles for connections)
        createNetworkNodes(posArray, colorArray, particleCount);

        // Create network links between nodes
        createNetworkLinks();

        // Create data packets group
        createDataPackets();

        // Add ambient light
        const ambientLight = new THREE.AmbientLight(0x404040);
        scene.add(ambientLight);

        // Add directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.5);
        directionalLight.position.set(1, 1, 1);
        scene.add(directionalLight);

        // Event listeners
        document.addEventListener('mousemove', onMouseMove);
        window.addEventListener('resize', onWindowResize);

        // Start animation
        animate();

        console.log('Three.js background initialized successfully');
    } catch (error) {
        console.error('Error initializing Three.js:', error);
    }
}

// Animation loop
function animate() {
    if (!scene || !camera || !renderer || !particles) return;

    requestAnimationFrame(animate);

    // Rotate particles
    particles.rotation.x += 0.0003;
    particles.rotation.y += 0.0005;

    // Respond to mouse movement
    particles.rotation.x += (mouseY * 0.00001);
    particles.rotation.y += (mouseX * 0.00001);

    // Rotate network links and data packets with particles
    if (networkLinks) {
        networkLinks.rotation.x = particles.rotation.x;
        networkLinks.rotation.y = particles.rotation.y;
    }

    if (dataPackets) {
        dataPackets.rotation.x = particles.rotation.x;
        dataPackets.rotation.y = particles.rotation.y;
    }

    // If we have small particles, animate them differently
    if (typeof smallParticles !== 'undefined') {
        smallParticles.rotation.x -= 0.0005;
        smallParticles.rotation.z += 0.0007;

        // Pulse the small particles
        const time = Date.now() * 0.0005;
        smallParticles.material.size = 1 + 0.5 * Math.sin(time);
    }

    // Update network links
    updateNetworkLinks();

    // Occasionally create new connections
    if (Math.random() < 0.03) { // 3% chance each frame
        createRandomConnection();
    }

    // Update data packets
    updateDataPackets();

    // Occasionally send a new data packet
    if (Math.random() < 0.05 && connectionLines.length > 0) { // 5% chance each frame
        sendDataPacket();
    }

    renderer.render(scene, camera);
}

// Handle mouse movement
function onMouseMove(event) {
    mouseX = event.clientX - window.innerWidth / 2;
    mouseY = event.clientY - window.innerHeight / 2;
}

// Handle window resize
function onWindowResize() {
    if (!camera || !renderer) return;

    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', init);

// Create network nodes from a subset of particles
function createNetworkNodes(posArray, colorArray, particleCount) {
    try {
        // Import Three.js from CDN
        const THREE = window.THREE;
        if (!THREE) {
            console.error('THREE.js not loaded');
            return;
        }

        // Select a subset of particles to be network nodes
        const nodeCount = Math.min(30, particleCount); // Limit to 30 nodes

        for (let i = 0; i < nodeCount; i++) {
            const randomIndex = Math.floor(Math.random() * particleCount);
            const posIndex = randomIndex * 3;

            // Create a node object with position and color
            const node = {
                index: randomIndex,
                position: new THREE.Vector3(
                    posArray[posIndex],
                    posArray[posIndex + 1],
                    posArray[posIndex + 2]
                ),
                color: new THREE.Color(
                    colorArray[posIndex],
                    colorArray[posIndex + 1],
                    colorArray[posIndex + 2]
                ),
                connections: [],
                lastUpdateTime: Date.now()
            };

            networkNodes.push(node);
        }

        console.log(`Created ${networkNodes.length} network nodes`);
    } catch (error) {
        console.error('Error creating network nodes:', error);
    }
}

// Create initial network links between nodes
function createNetworkLinks() {
    try {
        // Import Three.js from CDN
        const THREE = window.THREE;
        if (!THREE) {
            console.error('THREE.js not loaded');
            return;
        }

        // Create a group to hold all network links
        networkLinks = new THREE.Group();
        scene.add(networkLinks);

        // Create initial connections
        for (let i = 0; i < 20; i++) {
            createRandomConnection();
        }

        console.log(`Created initial network links`);
    } catch (error) {
        console.error('Error creating network links:', error);
    }
}

// Create a random connection between two nodes
function createRandomConnection() {
    try {
        if (!networkNodes.length || !networkLinks) return;

        // Import Three.js from CDN
        const THREE = window.THREE;
        if (!THREE) {
            console.error('THREE.js not loaded');
            return;
        }

        // Remove old connections if we've reached the maximum
        if (connectionLines.length >= MAX_CONNECTIONS) {
            const oldestLine = connectionLines.shift(); // Remove the oldest connection
            networkLinks.remove(oldestLine.line);
            oldestLine.line.geometry.dispose();
            oldestLine.line.material.dispose();
        }

        // Select two random nodes
        const nodeIndex1 = Math.floor(Math.random() * networkNodes.length);
        let nodeIndex2 = Math.floor(Math.random() * networkNodes.length);

        // Make sure we don't connect a node to itself
        while (nodeIndex1 === nodeIndex2) {
            nodeIndex2 = Math.floor(Math.random() * networkNodes.length);
        }

        const node1 = networkNodes[nodeIndex1];
        const node2 = networkNodes[nodeIndex2];

        // Create a line geometry between the two nodes
        const lineGeometry = new THREE.BufferGeometry().setFromPoints([
            node1.position,
            node2.position
        ]);

        // Create a line material with a gradient between the two node colors
        const lineMaterial = new THREE.LineBasicMaterial({
            color: 0x00ffff, // Cyan base color
            transparent: true,
            opacity: 0.3 + Math.random() * 0.3, // Random opacity between 0.3 and 0.6
            linewidth: 1
        });

        // Create the line and add it to the scene
        const line = new THREE.Line(lineGeometry, lineMaterial);
        networkLinks.add(line);

        // Create a connection object
        const connection = {
            node1: nodeIndex1,
            node2: nodeIndex2,
            line: line,
            creationTime: Date.now(),
            duration: 5000 + Math.random() * 10000, // Random duration between 5-15 seconds
            pulseSpeed: 0.5 + Math.random() * 1.5, // Random pulse speed
            pulsePhase: Math.random() * Math.PI * 2 // Random starting phase
        };

        // Add to our connections array
        connectionLines.push(connection);

        // Add to the nodes' connections
        node1.connections.push(connectionLines.length - 1);
        node2.connections.push(connectionLines.length - 1);

        return connection;
    } catch (error) {
        console.error('Error creating random connection:', error);
        return null;
    }
}

// Update network links (animate, remove expired connections)
function updateNetworkLinks() {
    try {
        if (!connectionLines.length || !networkLinks) return;

        const currentTime = Date.now();
        const linesToRemove = [];

        // Update each connection
        for (let i = 0; i < connectionLines.length; i++) {
            const connection = connectionLines[i];

            // Check if the connection has expired
            if (currentTime - connection.creationTime > connection.duration) {
                linesToRemove.push(i);
                continue;
            }

            // Animate the line (pulse effect)
            const age = (currentTime - connection.creationTime) / 1000; // Age in seconds
            const pulse = Math.sin(age * connection.pulseSpeed + connection.pulsePhase);

            // Update opacity based on pulse and age
            const ageRatio = 1 - ((currentTime - connection.creationTime) / connection.duration);
            connection.line.material.opacity = (0.2 + 0.3 * Math.max(0, pulse)) * ageRatio;
        }

        // Remove expired connections (in reverse order to avoid index issues)
        for (let i = linesToRemove.length - 1; i >= 0; i--) {
            const index = linesToRemove[i];
            const connection = connectionLines[index];

            // Remove from scene
            networkLinks.remove(connection.line);

            // Dispose of resources
            connection.line.geometry.dispose();
            connection.line.material.dispose();

            // Remove from array
            connectionLines.splice(index, 1);
        }
    } catch (error) {
        console.error('Error updating network links:', error);
    }
}

// Create data packets group
function createDataPackets() {
    try {
        // Import Three.js from CDN
        const THREE = window.THREE;
        if (!THREE) {
            console.error('THREE.js not loaded');
            return;
        }

        // Create a group to hold all data packets
        dataPackets = new THREE.Group();
        scene.add(dataPackets);

        console.log('Created data packets group');
    } catch (error) {
        console.error('Error creating data packets:', error);
    }
}

// Send a data packet along a random connection
function sendDataPacket() {
    try {
        if (!connectionLines.length || !dataPackets) return;

        // Import Three.js from CDN
        const THREE = window.THREE;
        if (!THREE) {
            console.error('THREE.js not loaded');
            return;
        }

        // Remove old packets if we've reached the maximum
        if (activePackets.length >= MAX_PACKETS) {
            const oldestPacket = activePackets.shift(); // Remove the oldest packet
            dataPackets.remove(oldestPacket.mesh);
            oldestPacket.mesh.geometry.dispose();
            oldestPacket.mesh.material.dispose();
        }

        // Select a random connection
        const connectionIndex = Math.floor(Math.random() * connectionLines.length);
        const connection = connectionLines[connectionIndex];

        // Get the nodes for this connection
        const node1 = networkNodes[connection.node1];
        const node2 = networkNodes[connection.node2];

        // Create a small sphere for the data packet
        const packetGeometry = new THREE.SphereGeometry(2, 8, 8);

        // Randomly choose packet color
        let packetColor;
        const colorRandom = Math.random();
        if (colorRandom > 0.7) {
            packetColor = 0x6e0b75; // Purple
        } else if (colorRandom > 0.3) {
            packetColor = 0x00ffff; // Cyan
        } else {
            packetColor = 0xff0000; // Red
        }

        const packetMaterial = new THREE.MeshBasicMaterial({
            color: packetColor,
            transparent: true,
            opacity: 0.8
        });

        const packetMesh = new THREE.Mesh(packetGeometry, packetMaterial);

        // Set initial position at the start node
        packetMesh.position.copy(node1.position);

        // Add to scene
        dataPackets.add(packetMesh);

        // Create a packet object
        const packet = {
            mesh: packetMesh,
            connection: connectionIndex,
            startNode: connection.node1,
            endNode: connection.node2,
            startPosition: node1.position.clone(),
            endPosition: node2.position.clone(),
            startTime: Date.now(),
            duration: 2000 + Math.random() * 3000, // 2-5 seconds to travel
            progress: 0
        };

        // Add to active packets
        activePackets.push(packet);

        return packet;
    } catch (error) {
        console.error('Error sending data packet:', error);
        return null;
    }
}

// Update data packets (move along connections, remove completed ones)
function updateDataPackets() {
    try {
        if (!activePackets.length || !dataPackets) return;

        const currentTime = Date.now();
        const packetsToRemove = [];

        // Update each packet
        for (let i = 0; i < activePackets.length; i++) {
            const packet = activePackets[i];

            // Calculate progress (0 to 1)
            packet.progress = (currentTime - packet.startTime) / packet.duration;

            // Check if the packet has completed its journey
            if (packet.progress >= 1) {
                packetsToRemove.push(i);
                continue;
            }

            // Update position along the path
            packet.mesh.position.lerpVectors(
                packet.startPosition,
                packet.endPosition,
                packet.progress
            );

            // Pulse effect
            const pulse = Math.sin(packet.progress * Math.PI * 4);
            packet.mesh.scale.setScalar(0.8 + 0.4 * Math.max(0, pulse));

            // Update opacity
            packet.mesh.material.opacity = 0.5 + 0.5 * (1 - packet.progress);
        }

        // Remove completed packets (in reverse order to avoid index issues)
        for (let i = packetsToRemove.length - 1; i >= 0; i--) {
            const index = packetsToRemove[i];
            const packet = activePackets[index];

            // Remove from scene
            dataPackets.remove(packet.mesh);

            // Dispose of resources
            packet.mesh.geometry.dispose();
            packet.mesh.material.dispose();

            // Remove from array
            activePackets.splice(index, 1);
        }
    } catch (error) {
        console.error('Error updating data packets:', error);
    }
}

// Make functions available globally
window.threeInit = init;
window.threeAnimate = animate;
